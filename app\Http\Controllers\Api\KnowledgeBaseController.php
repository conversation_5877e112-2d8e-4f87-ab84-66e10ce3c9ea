<?php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\KnowledgeBaseRequest;
use App\Models\KnowledgeBase;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class KnowledgeBaseController extends Controller
{
    public function index(Request $request)
{
    $query = KnowledgeBase::query();

    if ($request->has('search') && $request->search) {
        $query->where('title', 'like', '%' . $request->search . '%')
              ->orWhere('content', 'like', '%' . $request->search . '%');
    }

    $entries = $query->paginate(20);
    return response()->json($entries);
}


    public function show($id)
    {
        $entry = KnowledgeBase::where('id', $id)->where('status', 'published')->firstOrFail();
        $entry->increment('views');
        return response()->json($entry);
    }

    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'title' => 'required|string|max:255',
                'slug' => 'required|string|max:255|unique:knowledge_base',
                'content' => 'required|string',
                'status' => 'required|in:published,draft',
            ]);
    
            $knowledgeBase = KnowledgeBase::create([
                'title' => $validated['title'],
                'slug' => $validated['slug'],
                'content' => $validated['content'],
                'status' => $validated['status'],
                'author_id' => auth()->id(), // Automatically assign author_id!
            ]);
    
            return response()->json(['message' => 'Inserted successfully'], 201);
    
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
    

    public function update(Request $request, $id)
    {
        Log::info('Update request received', ['id' => $id, 'data' => $request->all()]);

        try {
            $entry = KnowledgeBase::findOrFail($id);

            $entry->title = $request->input('title');
            $entry->content = $request->input('content');
            $entry->save();

            Log::info('Entry updated successfully', ['id' => $id]);

            return response()->json(['message' => 'Entry updated successfully'], 200);
        } catch (\Exception $e) {
            Log::error('Error updating entry', ['id' => $id, 'error' => $e->getMessage()]);
            return response()->json(['message' => 'Failed to update entry'], 500);
        }
    }

    public function destroy($id)
    {
        $entry = KnowledgeBase::findOrFail($id);
        $entry->delete();
        return response()->json(['message' => 'Entry soft deleted.']);
    }

    public function restore($id)
    {
        $entry = KnowledgeBase::withTrashed()->findOrFail($id);
        $entry->restore();
        return response()->json(['message' => 'Entry restored.']);
    }
}