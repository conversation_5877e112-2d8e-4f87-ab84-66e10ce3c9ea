<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ImageLink;

class ImageLinkController extends Controller
{
    public function index()
    {
        $images = ImageLink::orderBy('rbprikaza', 'asc')->get();
        return view('imagelinks.index', compact('images'));
    }

    public function create()
    {
        return view('imagelinks.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required',
            'url' => 'required|url',
            'rbprikaza' => 'required',
        ]);

        ImageLink::create([
            'title' => $request->title,
            'url' => $request->url,
            'rbprikaza' => $request->rbprikaza,
        ]);

        return redirect()->route('imagelinks.index')->with('success', 'Image link added successfully.');
    }

    public function show(ImageLink $imagelink)
    {
        return view('imagelinks.show', compact('imagelink'));
    }

    public function edit(ImageLink $imagelink)
    {
        return view('imagelinks.edit', compact('imagelink'));
    }

    public function update(Request $request, ImageLink $imagelink)
    {
        $request->validate([
            'title' => 'required',
            'url' => 'required|url',
            'rbprikaza' => 'required',

        ]);

        $imagelink->update([
            'title' => $request->title,
            'url' => $request->url,
            'rbprikaza' => $request->rbprikaza,
        ]);

        return redirect()->route('imagelinks.index')->with('success', 'Image link updated successfully.');
    }

    public function destroy(ImageLink $imagelink)
    {
        $imagelink->delete();

        return redirect()->route('imagelinks.index')->with('success', 'Image link deleted successfully.');
    }
}