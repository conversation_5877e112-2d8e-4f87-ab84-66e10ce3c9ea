<?php

namespace App\Http\Controllers;

use App\Models\KnowledgeBase;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use App\Jobs\UpdateContentJob;

class KnowledgeBaseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $search = strtoupper($request->input('search', session('search')));
        $exactMatch = $request->has('exact_match') ? true : session('exact_match', false);

        session([
            'search' => $search,
            'exact_match' => $exactMatch,
        ]);

        $query = KnowledgeBase::orderBy('id', 'desc');

        // Apply role-based filtering
        if (auth()->user()->hasRole('user')) {
            $query->where('author_id', auth()->id());
        }

        if (!empty($search)) {
            $query->where(function ($q) use ($search, $exactMatch) {
                if ($exactMatch) {
                    $q->whereRaw("UPPER(title) = ?", [$search])
                        ->orWhereRaw("UPPER(content) = ?", [$search]);
                } else {
                    $q->whereRaw("UPPER(title) LIKE ?", ['%' . $search . '%'])
                        ->orWhereRaw("UPPER(content) LIKE ?", ['%' . $search . '%']);
                }
            });
        }

        $entries = $query->paginate(10);

        return view('knowledge_base.index', compact('entries', 'search'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('knowledge_base.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $slug = Str::slug($request->input('title'));

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'content' => 'nullable|string',
            'tags' => 'nullable|array',
            'status' => 'required|in:draft,published',
        ]);

        // Check if slug already exists
        if (KnowledgeBase::where('slug', $slug)->exists()) {
            return back()
                ->withInput()
                ->withErrors(['title' => 'A record with this title already exists. Please choose a different title.']);
        }

        $validated['slug'] = $slug;

        $validated['author_id'] = auth()->id();

        KnowledgeBase::create($validated);

        return redirect()->route('knowledge_base.index')->with('success', 'Entry created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show($id, Request $request)
    {
        $entry = KnowledgeBase::findOrFail($id);
        $search = $request->input('search'); // Get search term
        $page = $request->input('page'); // Get current page

        return view('knowledge_base.show', compact('entry', 'search', 'page'));
    }


    public function edit(KnowledgeBase $knowledgeBase)
    {
        return view('knowledge_base.edit', ['entry' => $knowledgeBase]);
    }

    public function update(Request $request, KnowledgeBase $knowledgeBase)
    {
        $slug = Str::slug($request->input('title'));

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'content' => 'nullable|string',
            'tags' => 'nullable|array',
            'status' => 'required|in:draft,published',
        ]);

        // Check if slug already exists (excluding the current record)
        if (KnowledgeBase::where('slug', $slug)->where('id', '!=', $knowledgeBase->id)->exists()) {
            return back()
                ->withInput()
                ->withErrors(['title' => 'A record with this title already exists. Please choose a different title.']);
        }

        $validated['slug'] = $slug;

        $knowledgeBase->update($validated);

        return redirect()->route('knowledge_base.index')->with('success', 'Entry updated successfully!');
    }

    public function destroy(KnowledgeBase $knowledgeBase)
    {
        $knowledgeBase->delete();
        return redirect()->route('knowledge_base.index')->with('success', 'Entry deleted successfully!');
    }

    public function updateAllRecords()
    {
        dispatch(new UpdateContentJob());
        return back()->with('success', 'Update has been queued.');
    }
    public function checkSlug(Request $request)
    {
        $slug = Str::slug($request->input('title'));
        $entryId = $request->input('entry_id'); // Will be null for creation, present for editing

        $query = KnowledgeBase::where('slug', $slug);

        if ($entryId) {
            $query->where('id', '!=', $entryId);
        }

        $exists = $query->exists();

        return response()->json(['exists' => $exists]);
    }

}