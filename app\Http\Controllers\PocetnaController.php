<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\KnowledgeBase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class PocetnaController extends Controller
{
    public function index(Request $request)
    {
        $search = strtoupper($request->input('search', session('search')));
        $exactMatch = $request->has('exact_match') ? true : session('exact_match', false);
    
        session([
            'search' => $search,
            'exact_match' => $exactMatch,
        ]);
    
        $query = KnowledgeBase::query()->where('status', '=', 'published')->orderBy('id', 'desc');
    
        if (!empty($search)) {
            $query->where(function ($q) use ($search, $exactMatch) {
                if ($exactMatch) {
                    $q->whereRaw("UPPER(title) = ?", [$search])
                        ->orWhereRaw("UPPER(content) = ?", [$search])
                        ->whereRaw("content NOT REGEXP ?", ['https?://[^\s]+']);
                } else {
                    $q->whereRaw("UPPER(title) LIKE ?", ['%' . $search . '%'])
                        ->orWhereRaw("UPPER(content) LIKE ?", ['%' . $search . '%'])
                        ->whereRaw("content NOT REGEXP ?", ['https?://[^\s]+']);
                }
            });
        }
    
        $posts = $query->paginate(10);
    
        return view('welcome', compact('posts', 'search'));
    }
    
public function show($id, Request $request)
{
    $entry = KnowledgeBase::findOrFail($id);
    $search = $request->input('search'); // Get search term

    return view('show', compact('entry', 'search'));
}

}
