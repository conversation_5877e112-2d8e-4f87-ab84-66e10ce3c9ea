<?php

namespace App\Http\Controllers;

use App\Models\TextEntry;
use Illuminate\Http\Request;

class TextEntryController extends Controller
{
    public function index()
    {
        $entries = TextEntry::orderBy('updated_at', 'desc')->get();
        return view('text-entries.index', compact('entries'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'nullable|string|max:255',
            'content' => 'required|string',
        ]);

        $entry = TextEntry::create($request->only(['title', 'content']));

        return response()->json([
            'success' => true,
            'entry' => $entry,
            'message' => 'Text entry saved successfully!'
        ]);
    }

    public function update(Request $request, TextEntry $entry)
    {
        $request->validate([
            'title' => 'nullable|string|max:255',
            'content' => 'required|string',
        ]);

        $entry->update($request->only(['title', 'content']));

        return response()->json([
            'success' => true,
            'entry' => $entry,
            'message' => 'Text entry updated successfully!'
        ]);
    }

    public function destroy(TextEntry $entry)
    {
        $entry->delete();

        return response()->json([
            'success' => true,
            'message' => 'Text entry deleted successfully!'
        ]);
    }
}

