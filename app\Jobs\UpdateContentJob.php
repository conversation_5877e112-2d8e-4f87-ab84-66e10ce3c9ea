<?php
namespace App\Jobs;

use App\Models\KnowledgeBase;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UpdateContentJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct()
    {
        //
    }

    public function handle()
    {
        KnowledgeBase::chunk(100, function ($records) {
            foreach ($records as $record) {
                $record->content = preg_replace('/(<a href="https?:\/\/)/', "\n\n$1", $record->content);
                $record->save();
            }
        });
    }
}
