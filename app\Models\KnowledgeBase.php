<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;


class KnowledgeBase extends Model
{
    use HasFactory;
    protected $table = 'knowledge_base';
    protected $fillable = [
        'title',
        'slug',
        'content',
        'tags',
        'author_id',
        'status',
        'views',
    ];

    protected $casts = [
        'tags' => 'array',
    ];

    /**
     * Relationship: An entry belongs to a user (author).
     */
    public function author()
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    /**
     * Scope for published entries.
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }
}
