<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
                // Create roles
                $admin = Role::create(['name' => 'admin']);
                $user = Role::create(['name' => 'user']);
                $ordinaryUserRole = Role::create(['name' => 'ordinary user']);               
        
                // Create permissions
                Permission::create(['name' => 'view all data']);
                Permission::create(['name' => 'view own data']);
                Permission::create(['name' => 'view shared data']);
                Permission::create(['name' => 'view articles']);
                Permission::create(['name' => 'create articles']);
                Permission::create(['name' => 'edit articles']);
                Permission::create(['name' => 'delete articles']);
                Permission::create(['name' => 'view comments']);
                Permission::create(['name' => 'create comments']);
                Permission::create(['name' => 'edit comments']);
                Permission::create(['name' => 'delete comments']);
                Permission::create(['name' => 'create users']);
                Permission::create(['name' => 'edit users']);
                Permission::create(['name' => 'delete users']);
                Permission::create(['name' => 'view users']);   
                
                // Assign permissions to roles
                //$admin->givePermissionTo(['view all data', 'view own data', 'view shared data']);
                $admin->givePermissionTo(Permission::all());
                $user->givePermissionTo(['create articles', 'edit articles', 'view articles', 'create comments', 'edit comments', 'view comments']);
                $ordinaryUserRole->givePermissionTo(['view articles', 'view comments']);
                // Assign role to a specific user
                $user = User::find(8); 
                $user->assignRole('admin');
    }
}
