<div class="p-6 lg:p-8 bg-white border-b border-gray-200 shadow-lg rounded-2xl">
    <h1 class="text-3xl font-bold text-gray-900 text-center">
        Welcome to Knowledge Base Application!
    </h1>
</div>

<div class="bg-gray-100 grid grid-cols-1 md:grid-cols-2 gap-6 lg:gap-8 p-6 lg:p-8">
    @foreach ([
        ['url' => 'knowledge_base', 'title' => 'Knowledge Base', 'desc' => 'Access Knowledge Base application to create, read, update, and delete text entries.', 'icon' => 'M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z'],
        ['url' => 'https://laravel.com/docs', 'title' => 'Laravel Documentation', 'desc' => '<PERSON><PERSON> has wonderful documentation covering every aspect of the framework.', 'icon' => 'M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25'],
        ['url' => 'https://laracasts.com', 'title' => 'Laracasts', 'desc' => 'Laracasts offers hundreds of video tutorials on Laravel, PHP, and JavaScript development.', 'icon' => 'M15.75 10.5l4.72-4.72a.75.75 0 011.28.53v11.38a.75.75 0 01-1.28.53l-4.72-4.72M4.5 18.75h9a2.25 2.25 0 002.25-2.25v-9a2.25 2.25 0 00-2.25-2.25h-9A2.25 2.25 0 002.25 7.5v9a2.25 2.25 0 002.25 2.25z'],
        ['url' => 'https://tailwindcss.com/', 'title' => 'Tailwind', 'desc' => 'Tailwind, an amazing utility first CSS framework that doesn\'t get in your way.', 'icon' => 'M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5z'],
    ] as $link)
        <div class="bg-white rounded-2xl shadow-md p-6 flex flex-col items-start">
            <div class="flex items-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" class="size-8 stroke-gray-400">
                    <path stroke-linecap="round" stroke-linejoin="round" d="{{ $link['icon'] }}" />
                </svg>
                <h2 class="ms-4 text-2xl font-semibold text-gray-900">
                    <a href="{{ $link['url'] }}" class="hover:underline">{{ $link['title'] }}</a>
                </h2>
            </div>
            <p class="text-gray-500 text-sm leading-relaxed">
                {{ $link['desc'] }}
            </p>
        </div>
    @endforeach
</div>
