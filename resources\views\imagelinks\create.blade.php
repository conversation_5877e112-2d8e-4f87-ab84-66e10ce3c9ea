<x-app-layout>
    <div class="max-w-3xl mx-auto py-10 px-6 bg-yellow rounded-lg shadow-md">
        <h1 class="text-2xl font-bold-red mb-6">{{ __('Add New Image')}}</h1>

        <form action="{{ route('imagelinks.store') }}" method="POST">
            @csrf

            <!-- Title -->
            <div class="mb-4">
                <label for="title" class="block text-sm font-medium text-gray-700 mb-1">{{ __('Title')}}</label>
                <input type="text" name="title" id="title"
                    class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    value="{{ old('title') }}" required>
            </div>

            <!-- URL -->
            <div class="mb-4">
                <label for="url" class="block text-sm font-medium text-gray-700 mb-1">{{ __('Image URL')}}</label>
                <input type="url" name="url" id="url"
                    class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    value="{{ old('url') }}" required>
            </div>
            <!--RBPRIKAZA -->
            <div class="mb-4">
                <label for="url" class="block text-sm font-medium text-gray-700 mb-1">{{ __('ID To Show')}}</label>
                <input type="rbprikaza" name="rbprikaza" id="rbprikaza"
                    class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    value="{{ old('rbprikaza') }}" required>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end space-x-3 mt-6">
                <a href="{{ route('imagelinks.index') }}"
                    class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition duration-150">
                    {{ __('Cancel')}}
                </a>
                <button type="submit"
                    class="inline-flex items-center px-3 py-1 bg-yellow-500 text-sm text-white rounded hover:bg-yellow-600">
                    {{ __('Save')}}
                </button>
            </div>
        </form>
    </div>
</x-app-layout>