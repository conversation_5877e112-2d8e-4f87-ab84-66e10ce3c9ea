<x-app-layout>
    <div class="max-w-3xl mx-auto py-10 px-6 bg-white rounded-lg shadow-md">
        <h1 class="text-2xl font-bold text-gray-800 mb-6">Edit Image Link</h1>

        <form action="{{ route('imagelinks.update', $imagelink->id) }}" method="POST">
            @csrf
            @method('PUT')

            <!-- Title -->
            <div class="mb-4">
                <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Title</label>
                <input type="text" name="title" id="title"
                    class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    value="{{ old('title', $imagelink->title) }}" required>
            </div>

            <!-- URL -->
            <div class="mb-4">
                <label for="url" class="block text-sm font-medium text-gray-700 mb-1">Image URL</label>
                <input type="url" name="url" id="url"
                    class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    value="{{ old('url', $imagelink->url) }}" required>
            </div>
            <!--RBPRIKAZA -->
            <div class="mb-4">
                <label for="rbprikaza" class="block text-sm font-medium text-gray-700 mb-1">ID To Show</label>
                <input type="text" name="rbprikaza" id="rbprikaza"
                    class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    value="{{ old('url', $imagelink->rbprikaza) }}" required>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end space-x-3 mt-6">
                <a href="{{ route('imagelinks.index') }}"
                    class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition duration-150">
                    Cancel
                </a>
                <button type="submit"
                    class="inline-flex items-center px-3 py-1 bg-yellow-500 text-sm text-white rounded hover:bg-yellow-600">
                    Update
                </button>
            </div>
        </form>
    </div>
</x-app-layout>