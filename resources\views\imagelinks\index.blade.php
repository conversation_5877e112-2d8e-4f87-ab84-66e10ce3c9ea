<x-app-layout>
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <h1 class="text-2xl font-bold text-gray-800 mb-6">Image Links</h1>

        <!-- Add New Button -->
        <a href="{{ route('imagelinks.create') }}" 
           class="inline-flex items-center px-3 py-1 bg-yellow-500 text-sm text-white rounded hover:bg-yellow-600">
            Add New Image Link
        </a>

        <!-- Grid of Cards -->
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
            @foreach ($images as $image)
                <div class="bg-white rounded-lg shadow-md overflow-hidden transition-transform transform hover:scale-105">
                    <!-- Image -->
                    <img src="{{ $image->url }}" 
                         alt="{{ $image->title }}" 
                         class="w-full h-48 object-cover">

                    <!-- Card Body -->
                    <div class="p-4">
                        <h2 class="text-lg font-semibold text-gray-700 mb-2">{{ $image->title }}</h2>

                        <!-- View Button -->
                        <a href="{{ $image->url }}" target="_blank" 
                           class="inline-flex items-center px-3 py-1 bg-gray-200 text-sm text-gray-700 rounded hover:bg-gray-300 mb-3">
                            View Image
                        </a>

                        <!-- Edit & Delete Buttons -->
                        <div class="flex space-x-2">
                            <a href="{{ route('imagelinks.edit', $image->id) }}"
                               class="inline-flex items-center px-3 py-1 bg-yellow-500 text-sm text-white rounded hover:bg-yellow-600">
                                Edit
                            </a>

                            <form action="{{ route('imagelinks.destroy', $image->id) }}" method="POST" class="inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit"
                                        class="inline-flex items-center px-3 py-1 bg-red-600 text-sm text-white rounded hover:bg-red-700">
                                    Delete
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</x-app-layout>