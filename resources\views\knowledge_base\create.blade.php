<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('All Tasks') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                <div class="container">
                    <h1 class="text-2xl font-bold mb-6">{{ __('Add New Entry') }}</h1>

                    <form action="{{ route('knowledge_base.store') }}" method="POST">
                        @csrf

                        <div class="mb-4">
                            <label for="title" class="block text-sm font-medium text-gray-700">{{ __('Title') }}</label>
                            <input type="text" name="title" id="title" value="{{ old('title') }}"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm @error('title') border-red-500 @enderror"
                                required autofocus>
                            @error('title')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                            <div id="slugFeedback" class="text-xs mt-1"></div>
                        </div>
                        <div class="mb-4">
                            <label for="content"
                                class="block text-sm font-medium text-gray-700">{{ __('Content') }}</label>
                            <textarea name="content" id="content"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                rows="20"></textarea>
                        </div>

                        <div class="mb-4">
                            <label for="tags" class="block text-sm font-medium text-gray-700">{{ __('Tags') }}</label>
                            <input type="text" name="tags[]" id="tags"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                        </div>

                        <div class="mb-4">
                            <label for="status"
                                class="block text-sm font-medium text-gray-700">{{ __('Status') }}</label>
                            <select name="status" id="status"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                                <option value="draft">{{ __('Draft') }}</option>
                                <option value="published">{{ __('Published') }}</option>
                            </select>
                        </div>
                        <div class="flex justify-between space-x-2">
                            <button type="submit"
                                class="inline-flex items-center px-4 py-2 bg-yellow-500 border border-transparent rounded-md font-semibold text-xs text-black uppercase tracking-widest hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-400">
                                {{ __('Save') }}
                            </button>
                            <a href="{{ route('knowledge_base.index') }}"
                                class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded"
                                style="color: white; background-color: green; border: 2px solid black;">
                                {{ __('Back to Index') }}
                            </a>

                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    @push('scripts')
        <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script> {{-- Make sure jQuery is
        loaded --}}
        <script>
            $(function () {
                $('#title').on('blur', function () {
                    console.log('Title field exited'); // Debug: should appear in browser console
                    checkSlug();
                });

                $('#title').on('focus', function () {
                    $('#slugFeedback').text('').removeClass('text-danger text-success');
                    $('button[type="submit"]').prop('disabled', false);
                });

                function checkSlug() {
                    const title = $('#title').val().trim();
                    const slugFeedback = $('#slugFeedback');
                    const submitButton = $('button[type="submit"]');

                    if (title.length > 0) {
                        slugFeedback.text('Checking availability...').removeClass('text-danger text-success').addClass('text-gray-500');

                        $.ajax({
                            url: "{{ route('knowledge_base.checkSlug') }}",
                            method: "POST",
                            data: {
                                _token: "{{ csrf_token() }}",
                                title: title
                            },
                            success: function (response) {
                                if (response.exists) {
                                    slugFeedback.text('This title (and its slug) already exists.').removeClass('text-success text-red-500').addClass('text-danger');
                                    submitButton.prop('disabled', true);
                                } else {
                                    slugFeedback.text('This title is available!').removeClass('text-danger text-gray-500').addClass('text-success');
                                    submitButton.prop('disabled', false);
                                }
                            },
                            error: function (xhr) {
                                console.error("Error checking slug:", xhr.responseText);
                                slugFeedback.text('Error checking slug. Please try again.').removeClass('text-success text-gray-500').addClass('text-danger');
                                submitButton.prop('disabled', false);
                            }
                        });
                    } else {
                        slugFeedback.text('').removeClass('text-danger text-success text-gray-500');
                        submitButton.prop('disabled', false);
                    }
                }
            });
        </script>
    @endpush
</x-app-layout>