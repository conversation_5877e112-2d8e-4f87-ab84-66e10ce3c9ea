<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Edit Knowledge Base Entry') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                <div class="container">
                    <h1 class="text-2xl font-bold mb-6">{{ __('Edit Entry') }}</h1>

                    <form action="{{ route('knowledge_base.update', $entry) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="mb-4">
                            <label for="title" class="block text-sm font-medium text-gray-700">{{ __('Title') }}</label>
                            <input type="text" name="title" id="title" value="{{ $entry->title }}"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                required autofocus>
                        </div>

                        <div class="mb-4">
                            <label for="content"
                                class="block text-sm font-medium text-gray-700">{{ __('Content') }}</label>
                            <textarea name="content" id="content"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                rows="20" required>{{ $entry->content }}</textarea>
                        </div>

                        <div class="mb-4">
                            <label for="status"
                                class="block text-sm font-medium text-gray-700">{{ __('Status') }}</label>
                            <select name="status" id="status"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                required>
                                <option value="draft" {{ $entry->status == 'draft' ? 'selected' : '' }}>{{ __('Draft') }}
                                </option>
                                <option value="published" {{ $entry->status == 'published' ? 'selected' : '' }}>
                                    {{ __('Published') }}</option>
                            </select>
                        </div>

                        <div class="flex justify-between space-x-2">
                            <button type="submit"
                                class="inline-flex items-center px-4 py-2 bg-yellow-500 border border-transparent rounded-md font-semibold text-xs text-black uppercase tracking-widest hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-400">
                                {{ __('Update') }}
                            </button>
                            <a href="{{ route('knowledge_base.index') }}"
                                class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded"
                                style="color: white; background-color: green; border: 2px solid black;">
                                {{ __('Back to Index') }}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>