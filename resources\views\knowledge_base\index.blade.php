<x-app-layout>
    <x-slot name="header">
        <!--        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Knowledge Base') }}
        </h2> -->
        <div class="overflow-x-auto mt-2">
            <!-- Search Form -->
            <div class="bg-white dark:bg-gray-500 overflow-hidden shadow-sm sm:rounded-lg mb-2">
                <div class="p-2 text-gray-900 dark:text-black">
                    <form method="GET" action="{{ route('knowledge_base.index') }}">
                        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                            <div class="form-group mb-4 md:mr-4 w-full md:w-auto">
                                <input type="text" class="form-control w-full" name="search"
                                    value="{{ old('search', session('search')) }}"
                                    placeholder="{{ __('Search all Knowledge Base by title or content') }}">
                            </div>
                            <div class="flex flex-row items-center w-full md:w-auto justify-between md:justify-end">
                                <button type="submit"
                                    class="text-white bg-red-600 hover:bg-red-700 font-medium py-2 px-4 rounded inline-block mr-2">{{ __('Search All') }}</button>
                                <div class="form-group form-check mx-2 flex items-center">
                                    <input type="checkbox" class="form-check-input" name="exact_match" id="exact_match"
                                        {{ session('exact_match') ? 'checked' : '' }}>
                                    <label for="exact_match"
                                        class="form-check-label ml-2">{{ __('Exact match') }}</label>
                                </div>
                                <a href="{{ route('knowledge_base.clear_filters') }}"
                                    class="text-white bg-yellow-600 hover:bg-red-700 font-medium py-2 px-4 rounded inline-block ml-2">{{ __('Empty filter') }}</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-4">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-4">

                <div class="container mx-auto">
                    <!--<h1 class="text-2xl font-bold mb-6">{{ __('Knowledge Base Entries') }}</h1>-->
                    <div class="flex justify-end ">
                        @hasrole('admin|user')
                        @can('create articles')
                            <a href="{{ route('knowledge_base.create') }}"
                                class="text-red bg-yellow-600 hover:bg-yellow-700 font-medium py-2 px-4 rounded inline-block"
                                style="background-color:rgb(225, 240, 10);">
                                {{ __('Create New Entry') }}
                            </a>
                        @endcan
                        @endhasrole
                    </div>
                    @if ($entries->count())
                        <div class="w-full overflow-x-auto">
                            <div id="wrapper w-full">
                                <table id="knowledgeBaseTable" class="min-w-[700px] w-full">
                                    <thead class="bg-gray-800 text-white">
                                        <tr>
                                            <th class="px-2 py-2 whitespace-nowrap">{{ __('ID') }}</th>
                                            <th class="px-4 py-2 whitespace-nowrap">{{ __('Title / Content') }}</th>
                                            <th class="px-4 py-2 whitespace-nowrap">{{ __('Actions') }}</th>
                                        </tr>
                                    </thead>

                                    <tbody>
                                        @foreach ($entries as $entry)
                                            <tr class="border-b">
                                                <td class="border px-4 py-2 text-sm sm:text-base">
                                                    <div class="text-xs sm:text-sm md:text-base"> {{ $entry->id }}</div>
                                                    <div class="text-xs sm:text-sm md:text-base">
                                                        {{ Str::limit($entry->status, 10) }}</div>
                                                </td>
                                                <td class="border px-4 py-2 text-sm sm:text-base md:text-base">
                                                    <div class="font-bold text-lg sm:text-base">{{ ucfirst($entry->title) }}
                                                    </div>
                                                    <!--    <div class="text-xs sm:text-sm md:text-base">{{ Str::limit($entry->content, 200) }}</div>-->
                                                </td>
                                                <td class="border px-4 py-2 flex flex-wrap gap-2">
                                                    <a href="{{ route('knowledge_base.show', ['knowledge_base' => $entry->id, 'search' => $search]) }}"
                                                        class="text-white bg-blue-600 hover:bg-blue-700 font-medium py-1 px-2 rounded text-xs sm:text-sm equal-width-button"
                                                        style="background-color:rgb(10, 14, 240);">
                                                        {{ __('Show') }}
                                                    </a>

                                                    @can('edit articles')
                                                        <a href="{{ route('knowledge_base.edit', $entry) }}"
                                                            class="text-white bg-yellow-600 hover:bg-yellow-700 font-medium py-1 px-2 rounded text-xs sm:text-sm equal-width-button"
                                                            style="background-color:rgb(121, 240, 10);">
                                                            {{ __('Edit') }}
                                                        </a>
                                                    @endcan
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>

                                </table>
                            </div>
                        </div>

                        <!-- Pagination links -->
                        <div class="mt-4">
                            {{ $entries->links() }}
                        </div>
                    @else
                        <p class="mt-6 text-gray-700">{{ __('No entries found.') }}</p>
                    @endif
                </div>

            </div>
        </div>
    </div>
</x-app-layout>