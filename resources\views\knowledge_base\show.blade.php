<!-- filepath: ssh://hostinger/home/<USER>/domains/yourtrueside.com/public_html/know_base/resources/views/knowledge_base/show.blade.php -->
<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Knowledge Base Entry') }}
        </h2>
    </x-slot>
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                <div class="container">
                    <h1 class="text-2xl font-bold mb-6">{{ $entry->title }}</h1>
                    <div class="prose" id="content">

                        @php
                            if (!function_exists('kb_linkify')) {
                                function kb_linkify($text)
                                {

                                    // Escape HTML first to avoid XSS, then convert URLs to clickable links
                                    $escaped = e($text);
                                    $pattern = '~(https?://[^\s<]+)~i';

                                    return preg_replace_callback($pattern, function ($matches) {
                                        $url = $matches[1];
                                        $rawUrl = html_entity_decode($url, ENT_QUOTES, 'UTF-8');
                                        $parts = parse_url($rawUrl);

                                        // Detect YouTube URLs and embed player
                                        $host = $parts['host'] ?? '';
                                        $isYoutube = str_contains($host, 'youtube.com') || str_contains($host, 'youtu.be');

                                        if ($isYoutube) {
                                            $videoId = null;

                                            // For standard YouTube links: https://www.youtube.com/watch?v=VIDEO_ID
                                            if (isset($parts['query'])) {
                                                parse_str($parts['query'], $queryParams);
                                                if (!empty($queryParams['v'])) {
                                                    $videoId = $queryParams['v'];
                                                }
                                            }

                                            // For short links: https://youtu.be/VIDEO_ID
                                            if (!$videoId && isset($parts['path'])) {
                                                $path = trim($parts['path'], '/');
                                                if ($path !== '') {
                                                    $videoId = $path;
                                                }
                                            }

                                            if ($videoId) {
                                                // Build safe embed URL
                                                $embedUrl = 'https://www.youtube.com/embed/' . rawurlencode($videoId);

                                                // Responsive 16:9 iframe
                                                return '<div style="position:relative;padding-bottom:56.25%;height:0;overflow:hidden;max-width:100%;margin-bottom:1rem;">'
                                                    . '<iframe src="' . e($embedUrl) . '" '
                                                    . 'style="position:absolute;top:0;left:0;width:100%;height:100%;" '
                                                    . 'frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" '
                                                    . 'allowfullscreen loading="lazy"></iframe></div>';
                                            }
                                        }

                                        // For non-YouTube URLs or YouTube URLs without valid video ID, show full URL as clickable link
                                        $label = $rawUrl; // Use the full URL as the label
                                        return '<a href="' . e($rawUrl) . '" target="_blank" rel="noopener noreferrer" class="text-blue-600 underline break-words">'
                                            . e($label) . '</a>';
                                    }, $escaped);

                                }
                            }
                        @endphp

                        {!! nl2br(kb_linkify($entry->content)) !!}

                    </div>
                    <span class="text-sm text-gray-500">{{ ucfirst($entry->status) }}</span>
                </div>
            </div>
        </div>
    </div>

    <div id="buttonContainer"
        style="position: fixed; bottom: 20px; right: 30px; z-index: 100; display: flex; gap: 10px;">

        <button id="scrollToTopBtn"
            style="display: none; border: none; outline: none; background-color: #007bff; color: white; cursor: pointer; padding: 15px; border-radius: 10px; font-size: 16px;">
            <i class="fas fa-arrow-up"></i> Go to top
        </button>

        <a href="{{ route('knowledge_base.index', array_filter(['search' => $search, 'page' => request('page')])) }}"
            id="backToIndexBtn"
            style="border: none; outline: none; background-color: green; color: white; cursor: pointer; padding: 15px; border-radius: 10px; font-size: 16px; text-align: center;">
            <i class="fas fa-arrow-left"></i> Back to Index
        </a>

        <button id="findNextBtn"
            style="display: none; border: none; outline: none; background-color: #007bff; color: white; cursor: pointer; padding: 15px; border-radius: 10px; font-size: 16px;">
            Find Next
        </button>

    </div>
    <style>
        .highlighted {
            background-color: yellow;
        }

        .highlighted.focused {
            background-color: orange;
            /* Change this to your desired focus color */
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            let search = @json($search);
            if (search) {
                let contentElement = document.getElementById('content');
                let content = contentElement.innerHTML;
                let regex = new RegExp('(' + search.split(' ').join('|') + ')', 'gi');
                content = content.replace(regex, '<span class="highlighted">$1</span>');
                contentElement.innerHTML = content;

                let highlightedElements = document.querySelectorAll('.highlighted');
                let currentIndex = -1;

                // Show "Find Next" button if matches exist
                if (highlightedElements.length > 0) {
                    document.getElementById('findNextBtn').style.display = 'block';

                    // Scroll to the first occurrence
                    highlightedElements[0].classList.add('focused');
                    highlightedElements[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
                    currentIndex = 0;
                }

                // Find Next Button Click Event
                document.getElementById('findNextBtn').addEventListener('click', function () {
                    if (highlightedElements.length === 0) return;

                    highlightedElements[currentIndex].classList.remove('focused');
                    currentIndex = (currentIndex + 1) % highlightedElements.length;
                    highlightedElements[currentIndex].classList.add('focused');
                    highlightedElements[currentIndex].scrollIntoView({ behavior: 'smooth', block: 'center' });
                });
            }

            // Show "Go to Top" button when scrolling
            window.addEventListener('scroll', function () {
                document.getElementById('scrollToTopBtn').style.display = window.scrollY > 300 ? 'block' : 'none';
            });

            // Scroll to Top Button Click Event
            document.getElementById('scrollToTopBtn').addEventListener('click', function () {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });
        });
    </script>
</x-app-layout>