<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Edit Entry') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                <div class="container">
                    <h1 class="text-2xl font-bold mb-6">{{ __('Edit Entry') }}</h1>

                    <!-- Update Form -->
                    <form action="{{ route('knowledge_base.update', $entry->id) }}" method="POST">
                        @csrf
                        @method('PUT') <!-- Use the PUT method for updates -->

                        <!-- Title Field -->
                        <div class="mb-4">
                            <label for="title" class="block text-sm font-medium text-gray-700">{{ __('Title') }}</label>
                            <input 
                                type="text" 
                                name="title" 
                                id="title" 
                                value="{{ old('title', $entry->title) }}" 
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" 
                                required
                            >
                        </div>

                        <!-- Content Field -->
                        <div class="mb-4">
                            <label for="content" class="block text-sm font-medium text-gray-700">{{ __('Content') }}</label>
                            <textarea 
                                name="content" 
                                id="content" 
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                            >{{ old('content', $entry->content) }}</textarea>
                        </div>

                        <!-- Tags Field -->
                        <div class="mb-4">
                            <label for="tags" class="block text-sm font-medium text-gray-700">{{ __('Tags') }}</label>
                            <input 
                                type="text" 
                                name="tags[]" 
                                id="tags" 
                                value="{{ old('tags', implode(', ', $entry->tags ?? [])) }}" 
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                            >
                        </div>

                        <!-- Status Field -->
                        <div class="mb-4">
                            <label for="status" class="block text-sm font-medium text-gray-700">{{ __('Status') }}</label>
                            <select 
                                name="status" 
                                id="status" 
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                            >
                                <option value="draft" {{ $entry->status === 'draft' ? 'selected' : '' }}>
                                    {{ __('Draft') }}
                                </option>
                                <option value="published" {{ $entry->status === 'published' ? 'selected' : '' }}>
                                    {{ __('Published') }}
                                </option>
                            </select>
                        </div>

                        <!-- Submit Button -->
                        <button 
                            type="submit" 
                            class="inline-flex items-center px-4 py-2 bg-yellow-500 border border-transparent rounded-md font-semibold text-xs text-black uppercase tracking-widest hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-400"
                            style="color: white; background-color: blue; border: 2px solid black;">
                            {{ __('Update') }}
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
