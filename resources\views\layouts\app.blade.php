<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    <link rel="stylesheet" type="text/css" href="{{ asset('css/jquery.dataTables.css') }}">
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Styles -->
    @livewireStyles
    <style>
        #wrapper {
            overflow-x: auto !important;
            width: 100%;
        }

        .equal-width-button {
            min-width: 75px;
            /* Adjust this value as needed */
            text-align: center;
            /* Center the text within the button */
            display: inline-block;
            /* Makes width apply correctly */
        }

        .custom-search-input {
            margin-left: auto;
            /* Align to the right */
            margin-right: 0;
            display: block;
        }

        @media (max-width: 640px) {
            .custom-search-input {
                width: 100%;
                /* Make the search input full width on mobile */
            }
        }
    </style>
</head>

<body class="font-sans antialiased">
    <x-banner />
    <div class="min-h-screen bg-gray-100">
        @livewire('navigation-menu')
        <!-- Page Heading -->
        @if (isset($header))
            <header class="bg-white shadow">
                <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                    {{ $header }}
                </div>
            </header>
        @endif
        <!-- Page Content -->
        <main>
            {{ $slot }}
        </main>
    </div>
    <script src="{{ asset('js/jquery-3.6.0.min.js') }}"></script>
    <!-- DataTables JS -->
    <script type="text/javascript" charset="utf8" src="{{ asset('js/jquery.dataTables.js') }}"></script>
    <script type="text/javascript" charset="utf8" src="{{ asset('js/dataTables.buttons.min.js') }}"></script>
    <script type="text/javascript" charset="utf8" src="{{ asset('js/jszip.min.js') }}"></script>
    <script type="text/javascript" charset="utf8" src="{{ asset('js/buttons.html5.min.js') }}"></script>
    <script type="text/javascript" charset="utf8" src="{{ asset('js/buttons.print.min.js') }}"></script>
    <!-- DataTables Buttons CSS -->
    <link rel="stylesheet" type="text/css" href="{{ asset('css/buttons.dataTables.min.css') }}">

    <script>
        $(document).ready(function () {
            $('#knowledgeBaseTable').DataTable(
                {
                    "responsive": true,
                    //    "language": {
                    //        "search": "Search by title and content only on this page"
                    //    },
                    "paging": false, // Disable DataTables pagination
                    "info": false, // Hide "Show entries" info
                    "searching": false, // Disable search
                    "dom": 'Bfrtip', // Add the buttons container
                    buttons: [
                        //    'excelHtml5',
                        //    'csvHtml5',
                        //    'print'
                    ],
                    "initComplete": function () {
                        // Wrap the search input field in a flex container
                        $('#knowledgeBaseTable_filter').addClass('flex justify-end');
                        // Add custom class to the search input field
                        $('#knowledgeBaseTable_filter input').addClass('custom-search-input');
                    }
                });

        });
    </script>
    @stack('modals')
    @livewireScripts
    @stack('scripts')
</body>

</html>