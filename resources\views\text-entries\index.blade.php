<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Text entries') }}
        </h2>
        <div class="overflow-x-auto mt-6">
    </x-slot>
    <!-- New Entry Form -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-700 mb-4">{{ __('Add New Text Entry') }}</h2>
        <form id="newEntryForm">
            <div class="mb-4">
                <input type="text" id="newTitle" placeholder="Title (optional)"
                    class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>
            <div class="mb-4">
                <textarea id="newContent" placeholder="Paste your text here..." rows="6"
                    class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical"
                    required></textarea>
            </div>
            <button type="submit"
                class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-md transition-colors">
                {{ __('Save Entry') }}
            </button>
        </form>
    </div>

    <!-- Existing Entries -->
    <div class="space-y-6" id="entriesContainer">
        @foreach($entries as $entry)
            <div class="bg-white rounded-lg shadow-md p-6" data-entry-id="{{ $entry->id }}">
                <div class="flex justify-between items-start mb-4">
                    <div class="flex-1">
                        <input type="text" value="{{ $entry->title }}" placeholder="Title (optional)"
                            class="entry-title w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent mb-2">
                        <textarea
                            class="entry-content w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical"
                            rows="4">{{ $entry->content }}</textarea>
                    </div>
                </div>

                <div class="flex space-x-2">
                    <button
                        class="update-btn bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md transition-colors"
                        data-entry-id="{{ $entry->id }}">
                        {{ __('Update')}}
                    </button>
                    <button class="copy-btn bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors"
                        data-content="{{ htmlspecialchars($entry->content) }}">
                        {{ __('Copy')}}
                    </button>
                    <button class="delete-btn bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md transition-colors"
                        data-entry-id="{{ $entry->id }}">
                        {{ __('Delete')}}
                    </button>
                </div>

                <div class="text-sm text-gray-500 mt-2">
                    Last updated: {{ $entry->updated_at->format('M d, Y H:i') }}
                </div>
            </div>
        @endforeach
    </div>
    </div>

    <script>
        // Set up CSRF token for axios
        axios.defaults.headers.common['X-CSRF-TOKEN'] = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        // New entry form submission
        document.getElementById('newEntryForm').addEventListener('submit', async function (e) {
            e.preventDefault();

            const title = document.getElementById('newTitle').value;
            const content = document.getElementById('newContent').value;

            if (!content.trim()) {
                alert('Content is required!');
                return;
            }

            try {
                const response = await axios.post('/text-entries', {
                    title: title,
                    content: content
                });

                if (response.data.success) {
                    // Clear form
                    document.getElementById('newTitle').value = '';
                    document.getElementById('newContent').value = '';

                    // Show success message
                    alert(response.data.message);

                    // Reload page to show new entry
                    window.location.reload();
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Error saving entry. Please try again.');
            }
        });

        // Update entry
        document.addEventListener('click', async function (e) {
            if (e.target.classList.contains('update-btn')) {
                const entryId = e.target.dataset.entryId;
                const container = e.target.closest('[data-entry-id]');
                const title = container.querySelector('.entry-title').value;
                const content = container.querySelector('.entry-content').value;

                try {
                    const response = await axios.put(`/text-entries/${entryId}`, {
                        title: title,
                        content: content
                    });

                    if (response.data.success) {
                        alert(response.data.message);
                        window.location.reload();
                    }
                } catch (error) {
                    console.error('Error:', error);
                    alert('Error updating entry. Please try again.');
                }
            }

            // Copy to clipboard
            if (e.target.classList.contains('copy-btn')) {
                const content = e.target.dataset.content;

                try {
                    await navigator.clipboard.writeText(content);

                    // Visual feedback
                    const originalText = e.target.textContent;
                    e.target.textContent = 'Copied!';
                    e.target.classList.remove('bg-blue-500', 'hover:bg-blue-600');
                    e.target.classList.add('bg-green-500');

                    setTimeout(() => {
                        e.target.textContent = originalText;
                        e.target.classList.remove('bg-green-500');
                        e.target.classList.add('bg-blue-500', 'hover:bg-blue-600');
                    }, 1000);
                } catch (error) {
                    console.error('Error copying to clipboard:', error);
                    alert('Error copying to clipboard. Please try again.');
                }
            }

            // Delete entry
            if (e.target.classList.contains('delete-btn')) {
                const entryId = e.target.dataset.entryId;

                if (confirm('Are you sure you want to delete this entry?')) {
                    try {
                        const response = await axios.delete(`/text-entries/${entryId}`);

                        if (response.data.success) {
                            alert(response.data.message);
                            window.location.reload();
                        }
                    } catch (error) {
                        console.error('Error:', error);
                        alert('Error deleting entry. Please try again.');
                    }
                }
            }
        });
    </script>
</x-app-layout>