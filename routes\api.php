<?php
use App\Http\Controllers\Api\KnowledgeBaseController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;

Route::post('/login', [AuthController::class, 'login']);
Route::post('/register', [AuthController::class, 'register']);
Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});
//Route::post('login', [App\Http\Controllers\Auth\LoginController::class, 'login']);

Route::middleware('auth:sanctum')->group(function () {
    Route::post('/knowledge-base', [KnowledgeBaseController::class, 'store']);
    Route::put('/knowledge-base/{id}', [KnowledgeBaseController::class, 'update']);
    Route::delete('/knowledge-base/{id}', [KnowledgeBaseController::class, 'destroy']);
    Route::patch('/knowledge-base/{id}/restore', [KnowledgeBaseController::class, 'restore']);
});

Route::get('/knowledge-base', [KnowledgeBaseController::class, 'index']);
Route::get('/knowledge-base/{id}', [KnowledgeBaseController::class, 'show']);