<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\KnowledgeBaseController;
use App\Http\Controllers\PocetnaController;
use App\Http\Controllers\ImageLinkController;
use App\Http\Controllers\TextEntryController;


Route::post('/knowledge_base/check-slug', [KnowledgeBaseController::class, 'checkSlug'])->name('knowledge_base.checkSlug');

Route::get('/', [\App\Http\Controllers\PocetnaController::class, 'index'])->name('welcome');

Route::get('/knowledge-base/clear-filters', function () {
    session()->forget(['search', 'exact_match']);
    return redirect()->route('knowledge_base.index');
})->name('knowledge_base.clear_filters');

Route::get('/pocetna/clear-filters', function () {
    session()->forget(['search', 'exact_match']);
    return redirect()->route('welcome');
})->name('pocetna.clear_filters');

Route::get('pocetna/{knowledge_base}', [PocetnaController::class, 'show'])->name('pocetna.show');

Route::get('/text-entries', [TextEntryController::class, 'index'])->name('text-entries.index');
Route::post('/text-entries', [TextEntryController::class, 'store'])->name('text-entries.store');
Route::put('/text-entries/{entry}', [TextEntryController::class, 'update'])->name('text-entries.update');
Route::delete('/text-entries/{entry}', [TextEntryController::class, 'destroy'])->name('text-entries.destroy');

Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',
])->group(function () {
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');

    Route::middleware(['auth'])->group(function () {
        Route::resource('imagelinks', ImageLinkController::class);
        Route::get('knowledge_base/update', [KnowledgeBaseController::class, 'updateAllRecords'])->name('knowledge_base.dopuni')->middleware('permission:view articles');
        Route::get('knowledge_base', [KnowledgeBaseController::class, 'index'])->name('knowledge_base.index')->middleware('permission:view articles');
        Route::get('knowledge_base/create', [KnowledgeBaseController::class, 'create'])->name('knowledge_base.create')->middleware('permission:create articles');
        Route::post('knowledge_base', [KnowledgeBaseController::class, 'store'])->name('knowledge_base.store')->middleware('permission:create articles');
        Route::get('knowledge_base/{knowledge_base}', [KnowledgeBaseController::class, 'show'])->name('knowledge_base.show')->middleware('permission:view articles');
        Route::get('knowledge_base/{knowledge_base}/edit', [KnowledgeBaseController::class, 'edit'])->name('knowledge_base.edit')->middleware('permission:edit articles');
        Route::put('knowledge_base/{knowledge_base}', [KnowledgeBaseController::class, 'update'])->name('knowledge_base.update')->middleware('permission:edit articles');
        Route::delete('knowledge_base/{knowledge_base}', [KnowledgeBaseController::class, 'destroy'])->name('knowledge_base.destroy')->middleware('permission:delete articles');
    });
});